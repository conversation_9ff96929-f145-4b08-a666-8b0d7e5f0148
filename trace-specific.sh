#!/bin/bash
# Frida Trace Scripts for Dominations
# Make executable with: chmod +x trace-specific.sh

APP_PACKAGE="com.nexonm.dominations.adk"

echo "Frida Trace Helper for Dominations"
echo "=================================="

# Function to check if device is connected
check_device() {
    if ! frida-ps -U > /dev/null 2>&1; then
        echo "❌ No USB device found. Make sure your device is connected and USB debugging is enabled."
        exit 1
    fi
    echo "✅ Device connected"
}

# Function to check if app is installed
check_app() {
    if ! frida-ps -U | grep -q "$APP_PACKAGE"; then
        echo "⚠️  App not running. Will spawn new instance."
    else
        echo "✅ App is running"
    fi
}

# Main menu
show_menu() {
    echo ""
    echo "Select tracing option:"
    echo "1) Trace GoodyHut functions (recommended)"
    echo "2) Trace Collection functions"
    echo "3) Trace specific IL2CPP addresses"
    echo "4) Trace all functions (very verbose!)"
    echo "5) Attach to running process"
    echo "6) Custom trace with handler"
    echo "7) Exit"
    echo ""
}

# Trace functions
trace_goody() {
    echo "🔍 Tracing GoodyHut functions..."
    frida-trace -U -f "$APP_PACKAGE" -i '*GoodyHut*' -o "trace-goody-$(date +%Y%m%d-%H%M%S).txt"
}

trace_collect() {
    echo "🔍 Tracing Collection functions..."
    frida-trace -U -f "$APP_PACKAGE" -i '*Collect*' -o "trace-collect-$(date +%Y%m%d-%H%M%S).txt"
}

trace_addresses() {
    echo "🔍 Tracing specific IL2CPP addresses..."
    frida-trace -U -f "$APP_PACKAGE" \
        -a "libil2cpp.so!0x209D258" \
        -a "libil2cpp.so!0x209B924" \
        -a "libil2cpp.so!0x209D434" \
        -a "libil2cpp.so!0x209CC3C" \
        -a "libil2cpp.so!0x209D9C4" \
        -o "trace-addresses-$(date +%Y%m%d-%H%M%S).txt"
}

trace_all() {
    echo "🔍 Tracing ALL functions (WARNING: Very verbose!)..."
    echo "Press Ctrl+C to stop when you have enough data"
    sleep 3
    frida-trace -U -f "$APP_PACKAGE" -i '*' -o "trace-all-$(date +%Y%m%d-%H%M%S).txt"
}

trace_attach() {
    echo "🔍 Attaching to running process..."
    frida-trace -U "$APP_PACKAGE" -i '*GoodyHut*' -o "trace-attach-$(date +%Y%m%d-%H%M%S).txt"
}

trace_custom() {
    echo "🔍 Tracing with custom handler..."
    if [ -f "trace-handler.js" ]; then
        frida-trace -U -f "$APP_PACKAGE" -i '*GoodyHut*' -S trace-handler.js -o "trace-custom-$(date +%Y%m%d-%H%M%S).txt"
    else
        echo "❌ trace-handler.js not found!"
        return 1
    fi
}

# Main execution
check_device
check_app

while true; do
    show_menu
    read -p "Enter your choice (1-7): " choice
    
    case $choice in
        1) trace_goody ;;
        2) trace_collect ;;
        3) trace_addresses ;;
        4) trace_all ;;
        5) trace_attach ;;
        6) trace_custom ;;
        7) echo "Goodbye!"; exit 0 ;;
        *) echo "❌ Invalid choice. Please try again." ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
done
