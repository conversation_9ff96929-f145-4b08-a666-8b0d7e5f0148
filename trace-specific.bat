@echo off
REM Frida Trace Scripts for Dominations (Windows)
REM Usage: trace-specific.bat

set APP_PACKAGE=com.nexonm.dominations.adk

echo Frida Trace Helper for Dominations
echo ==================================

REM Check if frida is available
frida --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Frida not found. Please install Frida first.
    pause
    exit /b 1
)

REM Check device connection
frida-ps -U >nul 2>&1
if errorlevel 1 (
    echo ❌ No USB device found. Make sure your device is connected and USB debugging is enabled.
    pause
    exit /b 1
)
echo ✅ Device connected

:menu
echo.
echo Select tracing option:
echo 1) Trace GoodyHut functions (recommended)
echo 2) Trace Collection functions  
echo 3) Trace specific IL2CPP addresses
echo 4) Trace all functions (very verbose!)
echo 5) Attach to running process
echo 6) Custom trace with handler
echo 7) Exit
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto trace_goody
if "%choice%"=="2" goto trace_collect
if "%choice%"=="3" goto trace_addresses
if "%choice%"=="4" goto trace_all
if "%choice%"=="5" goto trace_attach
if "%choice%"=="6" goto trace_custom
if "%choice%"=="7" goto exit
echo ❌ Invalid choice. Please try again.
goto menu

:trace_goody
echo 🔍 Tracing GoodyHut functions...
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set timestamp=%datetime:~0,8%-%datetime:~8,6%
frida-trace -U -f %APP_PACKAGE% -i "*GoodyHut*" -o "trace-goody-%timestamp%.txt"
goto continue

:trace_collect
echo 🔍 Tracing Collection functions...
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set timestamp=%datetime:~0,8%-%datetime:~8,6%
frida-trace -U -f %APP_PACKAGE% -i "*Collect*" -o "trace-collect-%timestamp%.txt"
goto continue

:trace_addresses
echo 🔍 Tracing specific IL2CPP addresses...
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set timestamp=%datetime:~0,8%-%datetime:~8,6%
frida-trace -U -f %APP_PACKAGE% -a "libil2cpp.so!0x209D258" -a "libil2cpp.so!0x209B924" -a "libil2cpp.so!0x209D434" -a "libil2cpp.so!0x209CC3C" -a "libil2cpp.so!0x209D9C4" -o "trace-addresses-%timestamp%.txt"
goto continue

:trace_all
echo 🔍 Tracing ALL functions (WARNING: Very verbose!)
echo Press Ctrl+C to stop when you have enough data
timeout /t 3 /nobreak >nul
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set timestamp=%datetime:~0,8%-%datetime:~8,6%
frida-trace -U -f %APP_PACKAGE% -i "*" -o "trace-all-%timestamp%.txt"
goto continue

:trace_attach
echo 🔍 Attaching to running process...
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set timestamp=%datetime:~0,8%-%datetime:~8,6%
frida-trace -U %APP_PACKAGE% -i "*GoodyHut*" -o "trace-attach-%timestamp%.txt"
goto continue

:trace_custom
echo 🔍 Tracing with custom handler...
if not exist "trace-handler.js" (
    echo ❌ trace-handler.js not found!
    goto continue
)
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set timestamp=%datetime:~0,8%-%datetime:~8,6%
frida-trace -U -f %APP_PACKAGE% -i "*GoodyHut*" -S trace-handler.js -o "trace-custom-%timestamp%.txt"
goto continue

:continue
echo.
pause
goto menu

:exit
echo Goodbye!
pause
