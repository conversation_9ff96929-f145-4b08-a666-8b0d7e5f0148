# Frida Trace Commands for Dominations

## Basic Tracing Commands

### 1. Trace All Functions (Warning: <PERSON> Verbose)
```bash
frida-trace -U -f com.nexonm.dominations.adk -i '*'
```

### 2. Trace GoodyHut Related Functions
```bash
frida-trace -U -f com.nexonm.dominations.adk -i '*GoodyHut*'
```

### 3. Trace Collection Functions
```bash
frida-trace -U -f com.nexonm.dominations.adk -i '*Collect*'
```

### 4. Trace IL2CPP Functions
```bash
frida-trace -U -f com.nexonm.dominations.adk -i 'libil2cpp.so!*'
```

### 5. Trace Unity Functions
```bash
frida-trace -U -f com.nexonm.dominations.adk -i 'libunity.so!*'
```

### 6. Attach to Running Process (GoodyHut functions)
```bash
frida-trace -U com.nexonm.dominations.adk -i '*GoodyHut*'
```

## Advanced Tracing

### 7. Trace Specific Functions by Address
```bash
frida-trace -U -f com.nexonm.dominations.adk -a libil2cpp.so!0x209D258
```

### 8. Trace Multiple Specific Addresses
```bash
frida-trace -U -f com.nexonm.dominations.adk \
  -a libil2cpp.so!0x209D258 \
  -a libil2cpp.so!0x209B924 \
  -a libil2cpp.so!0x209D434
```

### 9. Trace with Custom JavaScript Handler
```bash
frida-trace -U -f com.nexonm.dominations.adk -i '*GoodyHut*' -S trace-handler.js
```

### 10. Trace and Save to File
```bash
frida-trace -U -f com.nexonm.dominations.adk -i '*GoodyHut*' -o trace-output.txt
```

## NPM Script Usage

After adding the scripts to package.json, you can use:

```bash
npm run trace-all      # Trace all functions
npm run trace-goody    # Trace GoodyHut functions
npm run trace-collect  # Trace Collect functions
npm run trace-il2cpp   # Trace IL2CPP functions
npm run trace-unity    # Trace Unity functions
npm run trace-attach   # Attach to running process
```

## Filtering and Analysis

### Filter by Module
```bash
frida-trace -U -f com.nexonm.dominations.adk -I 'libil2cpp.so'
```

### Exclude System Functions
```bash
frida-trace -U -f com.nexonm.dominations.adk -i '*GoodyHut*' -X 'libc.so!*'
```

### Trace with Backtrace
```bash
frida-trace -U -f com.nexonm.dominations.adk -i '*GoodyHut*' -T
```

## Known Function Addresses (from your script)

Based on your IL2CPP_ADDRESSES:
- CanCollect: 0x209D258
- StartCollect: 0x209D434  
- FinishCollect: 0x209B924
- GetRewardType: 0x209CC3C
- GetRewardAmount: 0x209D9C4
- GetHealth: 0x209EA1C
- GetCooldownTimeLeft: 0x209E9B0
- Config: 0x209B54C

## Tips

1. Start with specific function patterns to avoid overwhelming output
2. Use `-o filename.txt` to save trace output to file
3. Use `-T` flag to include backtraces for better context
4. Use `-S script.js` to add custom JavaScript handlers
5. For IL2CPP games, focus on `libil2cpp.so` module
6. Use `-X pattern` to exclude noisy system functions
