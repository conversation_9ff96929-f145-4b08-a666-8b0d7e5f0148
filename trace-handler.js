// Custom Frida Trace Handler for Dominations GoodyHut Functions
// Usage: frida-trace -U -f com.nexonm.dominations.adk -i '*GoodyHut*' -S trace-handler.js

// Enhanced logging with timestamps and context
function logWithContext(message, args) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${message}`);
    
    if (args && args.length > 0) {
        console.log(`  Args: ${args.map((arg, i) => `arg${i}=${arg}`).join(', ')}`);
    }
}

// Hook for any function containing "GoodyHut"
defineHandler({
    onEnter: function(log, args, state) {
        logWithContext(`→ ENTER ${this.displayName}`, args);
        
        // Store arguments for onLeave
        state.args = args;
        state.enterTime = Date.now();
        
        // Special handling for known functions
        if (this.displayName.includes('CanCollect')) {
            log(`  Instance: ${args[0]}`);
            state.isCanCollect = true;
        } else if (this.displayName.includes('Collect')) {
            log(`  Instance: ${args[0]}`);
            state.isCollect = true;
        } else if (this.displayName.includes('GetReward')) {
            log(`  Instance: ${args[0]}`);
            state.isReward = true;
        }
    },
    
    onLeave: function(log, retval, state) {
        const duration = Date.now() - state.enterTime;
        logWithContext(`← LEAVE ${this.displayName} (${duration}ms)`, null);
        
        // Log return value with context
        if (retval !== undefined) {
            log(`  Return: ${retval}`);
            
            // Special handling for known return types
            if (state.isCanCollect) {
                const canCollect = retval.toInt32();
                log(`  Can Collect: ${canCollect === 1 ? 'YES' : 'NO'}`);
                
                if (canCollect === 1) {
                    log(`  🎯 COLLECTIBLE GOODY HUT DETECTED!`);
                }
            } else if (state.isReward) {
                log(`  Reward Value: ${retval.toInt32()}`);
            }
        }
        
        log(''); // Empty line for readability
    }
});

// Additional handler for IL2CPP specific addresses
if (Process.findModuleByName('libil2cpp.so')) {
    const il2cpp = Process.findModuleByName('libil2cpp.so');
    
    // Known addresses from your script
    const addresses = {
        CanCollect: 0x209D258,
        StartCollect: 0x209D434,
        FinishCollect: 0x209B924,
        GetRewardType: 0x209CC3C,
        GetRewardAmount: 0x209D9C4,
        GetHealth: 0x209EA1C
    };
    
    // Hook specific addresses if they're being traced
    Object.entries(addresses).forEach(([name, offset]) => {
        const addr = il2cpp.base.add(offset);
        
        try {
            Interceptor.attach(addr, {
                onEnter: function(args) {
                    logWithContext(`🎯 ${name} called`, [args[0]]);
                    this.startTime = Date.now();
                },
                onLeave: function(retval) {
                    const duration = Date.now() - this.startTime;
                    logWithContext(`🎯 ${name} returned (${duration}ms)`, [retval]);
                    
                    if (name === 'CanCollect' && retval.toInt32() === 1) {
                        console.log('  🚀 AUTO-COLLECTION OPPORTUNITY!');
                    }
                }
            });
            console.log(`[+] Hooked ${name} at ${addr}`);
        } catch (e) {
            console.log(`[-] Failed to hook ${name}: ${e.message}`);
        }
    });
}

console.log('[+] Enhanced GoodyHut trace handler loaded');
console.log('[*] Watching for GoodyHut function calls...');
